我要在我的rag系统中增加 知识图谱的功能，我需要实现以下几个功能

1：把数据存入向量数据库时也需要把数据存入图数据库中，图数据库我使用Neo4j
2:

信息抽取：AllenNLP (官方文档：https://docs.allennlp.org/main/)
图谱存储：Neo4j
数据集:CN-DBpedia


我的rag系统是为我的aiops的系统，是一个基于AI的运维系统，知识图谱相关的知识来自mysql数据库中。图数据库使用neo4j,
假设mysql中数据中知识包括以下3方面
1：主机节点上有那些镜像
2：各个镜像里有那些漏洞
3：各漏洞的元信息，及修复建议
假设现在发现了一个漏洞，我需要去处理，在处理时我得知道需要去处理哪些镜像，去那些节点上处理
其他要求如下：
1：你得告诉我neo4j怎么在docker中部署，怎么初始化这些知识，
2：mysql中我还没有数据库和表，你需要新建表，再写入一些数据供我测试使用
我应该怎么为我的rag系统增加知识图谱的功能,，你先告诉我你的执行步骤，我先检查


